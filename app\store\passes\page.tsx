import { StoreClient } from "@/components/store/store-client"

// Mock data for passes since we don't have a real API
const mockPasses = Array.from({ length: 156 }, (_, i) => ({
  id: 20000 + i,
  name: `Pass ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `Pass ${i + 1} description`,
  imageUrl: '/placeholder-pass.jpg',
  price: Math.floor(Math.random() * 50) + 20,
  type: 'Pass'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function PassesPage() {
  return <StoreClient initialItems={mockPasses} initialFilter={{ type: ['Pass'] }} />
}
