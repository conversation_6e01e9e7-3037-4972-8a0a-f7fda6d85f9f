"use client"

import * as React from "react"
import { ChevronDown, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

export interface FilterOption {
  value: string
  label: string
  icon?: string
  count?: number
}

export interface FilterCategory {
  id: string
  title: string
  options: FilterOption[]
  selected: string[]
  onSelectionChange: (selected: string[]) => void
}

interface FilterMenuProps {
  categories: FilterCategory[]
  className?: string
}

export function FilterMenu({
  categories,
  className
}: FilterMenuProps) {
  const [expandedCategories, setExpandedCategories] = React.useState<Set<string>>(
    new Set() // Start with all categories collapsed
  )

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }

  const handleOptionToggle = (categoryId: string, optionValue: string) => {
    const category = categories.find(cat => cat.id === categoryId)
    if (!category) return

    const newSelected = category.selected.includes(optionValue)
      ? category.selected.filter(item => item !== optionValue)
      : [...category.selected, optionValue]
    
    category.onSelectionChange(newSelected)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {categories.map((category) => {
        const isExpanded = expandedCategories.has(category.id)
        const selectedCount = category.selected.length

        return (
          <div key={category.id} className="space-y-2">
            {/* Category Header */}
            <button
              onClick={() => toggleCategory(category.id)}
              className="flex items-center justify-between w-full text-left p-3 rounded-md hover:bg-purple-600/20 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="text-white">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
                <span className="text-white font-medium text-lg">{category.title}</span>
                {selectedCount > 0 && (
                  <Badge variant="secondary" className="bg-purple-500/30 text-purple-200 text-xs">
                    {selectedCount}
                  </Badge>
                )}
              </div>
            </button>

            {/* Category Options */}
            {isExpanded && (
              <div
                className="ml-7 space-y-2 animate-in slide-in-from-top-2 duration-200"
              >
                {category.options.map((option) => {
                  const isSelected = category.selected.includes(option.value)

                  return (
                    <div
                      key={option.value}
                      className="flex items-center justify-between p-2 rounded-md hover:bg-purple-600/10 transition-colors cursor-pointer"
                      onClick={() => handleOptionToggle(category.id, option.value)}
                    >
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={() => handleOptionToggle(category.id, option.value)}
                          className="border-purple-500/50 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                        />
                        <div className="flex items-center space-x-2">
                          {option.icon && (
                            <Image
                              src={option.icon}
                              alt={option.label}
                              width={16}
                              height={16}
                              className="flex-shrink-0"
                            />
                          )}
                          <span className="text-white text-sm">{option.label}</span>
                        </div>
                      </div>
                      {option.count !== undefined && (
                        <Badge variant="outline" className="text-xs text-gray-400 border-gray-600">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}
