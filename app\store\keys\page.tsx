import { StoreClient } from "@/components/store/store-client"

// Mock data for keys since we don't have a real API
const mockKeys = Array.from({ length: 45 }, (_, i) => ({
  id: 40000 + i,
  name: `Key ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `Key ${i + 1} description`,
  imageUrl: '/placeholder-key.jpg',
  price: Math.floor(Math.random() * 20) + 3,
  type: 'Key'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function KeysPage() {
  return <StoreClient initialItems={mockKeys} initialFilter={{ type: ['Key'] }} />
}
