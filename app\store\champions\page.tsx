import { StoreClient } from "@/components/store/store-client"

// Mock data for champions since we don't have a real API
const mockChampions = Array.from({ length: 167 }, (_, i) => ({
  id: 10000 + i,
  name: `Champion ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `Champion ${i + 1} description`,
  imageUrl: '/placeholder-champion.jpg',
  price: Math.floor(Math.random() * 100) + 10,
  type: 'Champion'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function ChampionsPage() {
  return <StoreClient initialItems={mockChampions} initialFilter={{ type: ['Champion'] }} />
}
