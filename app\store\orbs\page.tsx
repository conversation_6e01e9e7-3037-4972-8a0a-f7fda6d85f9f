import { StoreClient } from "@/components/store/store-client"

// Mock data for orbs since we don't have a real API
const mockOrbs = Array.from({ length: 234 }, (_, i) => ({
  id: 50000 + i,
  name: `Orb ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `Orb ${i + 1} description`,
  imageUrl: '/placeholder-orb.jpg',
  price: Math.floor(Math.random() * 40) + 10,
  type: 'Orb'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function OrbsPage() {
  return <StoreClient initialItems={mockOrbs} initialFilter={{ type: ['Orb'] }} />
}
