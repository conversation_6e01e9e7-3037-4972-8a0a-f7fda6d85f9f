import { useState, useMemo, useCallback, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { SelectedFilter } from '@/components/store/selected-filters-display'

export interface StoreItem {
  id: number
  name: string
  rarity: string
  isLegacy: boolean
  description: string
  imageUrl: string
  price: number
  type: string
}

export interface FilterState {
  search: string
  rarity: string[]
  type: string[]
  priceRange: [number, number]
  sort: string
}

export interface UseFilterStateReturn {
  // State
  filterState: FilterState
  currentPage: number
  
  // Actions
  setSearch: (search: string) => void
  setRarity: (rarity: string[]) => void
  setType: (type: string[]) => void
  setPriceRange: (range: [number, number]) => void
  setSort: (sort: string) => void
  setCurrentPage: (page: number) => void
  
  // Filter management
  selectedFilters: SelectedFilter[]
  removeFilter: (filterId: string, category: string) => void
  clearCategory: (category: string) => void
  clearAllFilters: () => void
  
  // Data processing
  getFilteredItems: (items: StoreItem[]) => StoreItem[]
  getPaginatedItems: (items: StoreItem[], itemsPerPage: number) => StoreItem[]
  getTotalPages: (items: StoreItem[], itemsPerPage: number) => number
}

const initialFilterState: FilterState = {
  search: '',
  rarity: [],
  type: [],
  priceRange: [0, 200],
  sort: ''
}

export function useFilterState(initialFilter?: Partial<FilterState>): UseFilterStateReturn {
  const router = useRouter()
  const pathname = usePathname()

  const [filterState, setFilterState] = useState<FilterState>(() => {
    // Check for pending filter state from navigation
    if (typeof window !== 'undefined') {
      const pendingState = sessionStorage.getItem('pendingFilterState')
      if (pendingState) {
        sessionStorage.removeItem('pendingFilterState')
        try {
          const parsedState = JSON.parse(pendingState)
          return { ...initialFilterState, ...parsedState }
        } catch (e) {
          // If parsing fails, fall back to initial state
        }
      }
    }

    return {
      ...initialFilterState,
      ...initialFilter
    }
  })
  const [currentPage, setCurrentPage] = useState(1)

  // Helper function to navigate based on filter state
  const navigateBasedOnFilters = useCallback((newFilterState: FilterState) => {
    // If we're on a specific type page and that type is being removed
    if (pathname.startsWith('/store/') && pathname !== '/store') {
      const currentType = pathname.split('/')[2] // e.g., 'skins' from '/store/skins'
      const typeMapping: Record<string, string> = {
        'skins': 'Skin',
        'chromas': 'Chroma',
        'champions': 'Champion',
        'passes': 'Pass',
        'chests': 'Chest',
        'keys': 'Key',
        'orbs': 'Orb',
        'bundles': 'Bundle',
        'tft': 'TFT'
      }

      const expectedType = typeMapping[currentType]

      // If the current type is being removed from filters, navigate to /store
      if (expectedType && !newFilterState.type.includes(expectedType)) {
        // Store the filter state before navigation
        sessionStorage.setItem('pendingFilterState', JSON.stringify(newFilterState))
        router.push('/store')
        return
      }
    }

    // If we're on /store and a single type filter is selected, navigate to that type's page
    if (pathname === '/store' && newFilterState.type.length === 1) {
      const typeToPath: Record<string, string> = {
        'Skin': '/store/skins',
        'Chroma': '/store/chromas',
        'Champion': '/store/champions',
        'Pass': '/store/passes',
        'Chest': '/store/chests',
        'Key': '/store/keys',
        'Orb': '/store/orbs',
        'Bundle': '/store/bundles',
        'TFT': '/store/tft'
      }

      const targetPath = typeToPath[newFilterState.type[0]]
      if (targetPath) {
        router.push(targetPath)
        return
      }
    }

    // If multiple types are selected or other complex filters, navigate to /store
    if (pathname !== '/store' && (newFilterState.type.length > 1 || newFilterState.rarity.length > 0 || newFilterState.search || newFilterState.priceRange[0] !== 0 || newFilterState.priceRange[1] !== 200)) {
      // Store the filter state before navigation
      sessionStorage.setItem('pendingFilterState', JSON.stringify(newFilterState))
      router.push('/store')
    }
  }, [router, pathname])

  // Individual setters
  const setSearch = useCallback((search: string) => {
    setFilterState(prev => ({ ...prev, search }))
    setCurrentPage(1)
  }, [])

  const setRarity = useCallback((rarity: string[]) => {
    const newFilterState = { ...filterState, rarity }
    setFilterState(newFilterState)
    setCurrentPage(1)
    navigateBasedOnFilters(newFilterState)
  }, [filterState, navigateBasedOnFilters])

  const setType = useCallback((type: string[]) => {
    const newFilterState = { ...filterState, type }
    setFilterState(newFilterState)
    setCurrentPage(1)
    navigateBasedOnFilters(newFilterState)
  }, [filterState, navigateBasedOnFilters])



  const setPriceRange = useCallback((priceRange: [number, number]) => {
    setFilterState(prev => ({ ...prev, priceRange }))
    setCurrentPage(1)
  }, [])

  const setSort = useCallback((sort: string) => {
    setFilterState(prev => ({ ...prev, sort }))
  }, [])

  // Helper function to get rarity icon
  const getRarityIcon = useCallback((rarity: string) => {
    const rarityMap: Record<string, string> = {
      'legacy': '/Legacy.png',
      'rare': '/Rare.png',
      'epic': '/Epic.png',
      'legendary': '/Legendary.png',
      'mythic': '/Mythic.png',
      'ultimate': '/Ultimate.png',
      'exalted': '/Exalted.png',
      'transcendent': '/Transcendent.png',
      'chroma': '/Chroma.png'
    }
    return rarityMap[rarity.toLowerCase()] || '/Rare.png'
  }, [])

  // Generate selected filters array
  const selectedFilters = useMemo((): SelectedFilter[] => {
    const filters: SelectedFilter[] = []

    // Rarity filters
    filterState.rarity.forEach(rarity => {
      filters.push({
        id: `rarity-${rarity}`,
        label: rarity,
        value: rarity,
        category: 'rarity',
        icon: getRarityIcon(rarity)
      })
    })

    // Type filters
    filterState.type.forEach(type => {
      filters.push({
        id: `type-${type}`,
        label: type,
        value: type,
        category: 'type'
      })
    })



    // Price range filter
    if (filterState.priceRange[0] !== 0 || filterState.priceRange[1] !== 200) {
      filters.push({
        id: 'price-range',
        label: `$${filterState.priceRange[0]} - $${filterState.priceRange[1]}`,
        value: `${filterState.priceRange[0]}-${filterState.priceRange[1]}`,
        category: 'price'
      })
    }

    // Sort filter
    if (filterState.sort) {
      const sortLabels: Record<string, string> = {
        'price-high': 'Highest Price',
        'price-low': 'Lowest Price',
        'name-asc': 'Name A-Z',
        'name-desc': 'Name Z-A'
      }

      filters.push({
        id: `sort-${filterState.sort}`,
        label: sortLabels[filterState.sort] || filterState.sort,
        value: filterState.sort,
        category: 'sort'
      })
    }

    return filters
  }, [filterState, getRarityIcon])

  // Filter management functions
  const removeFilter = useCallback((filterId: string, category: string) => {
    const [categoryName, value] = filterId.split('-')

    switch (category) {
      case 'rarity':
        setFilterState(prev => ({ ...prev, rarity: prev.rarity.filter(r => r !== value) }))
        setCurrentPage(1)
        break
      case 'type':
        setFilterState(prev => ({ ...prev, type: prev.type.filter(t => t !== value) }))
        setCurrentPage(1)
        break
      case 'price':
        setFilterState(prev => ({ ...prev, priceRange: [0, 200] }))
        setCurrentPage(1)
        break
      case 'sort':
        setFilterState(prev => ({ ...prev, sort: '' }))
        break
    }
  }, [])

  const clearCategory = useCallback((category: string) => {
    let newFilterState = { ...filterState }

    switch (category) {
      case 'rarity':
        newFilterState = { ...newFilterState, rarity: [] }
        break
      case 'type':
        newFilterState = { ...newFilterState, type: [] }
        break
      case 'price':
        newFilterState = { ...newFilterState, priceRange: [0, 200] as [number, number] }
        break
      case 'sort':
        newFilterState = { ...newFilterState, sort: '' }
        break
    }

    setFilterState(newFilterState)
    setCurrentPage(1)
    navigateBasedOnFilters(newFilterState)
  }, [filterState, navigateBasedOnFilters])

  const clearAllFilters = useCallback(() => {
    setFilterState(initialFilterState)
    setCurrentPage(1)
    // When clearing all filters, navigate to main store page
    if (pathname !== '/store') {
      router.push('/store')
    }
  }, [pathname, router])

  // Data processing functions
  const getFilteredItems = useCallback((items: StoreItem[]) => {
    let filtered = items.filter(item => {
      // Search filter
      if (filterState.search && !item.name.toLowerCase().includes(filterState.search.toLowerCase())) {
        return false
      }
      
      // Price filter
      if (item.price < filterState.priceRange[0] || item.price > filterState.priceRange[1]) {
        return false
      }
      
      // Type filter
      if (filterState.type.length > 0 && !filterState.type.some(type => 
        item.type.toLowerCase() === type.toLowerCase()
      )) {
        return false
      }
      
      // Rarity filter (including Legacy)
      if (filterState.rarity.length > 0) {
        const hasLegacyFilter = filterState.rarity.includes('Legacy')
        const hasOtherRarityFilters = filterState.rarity.some(r => r !== 'Legacy')

        // If Legacy is selected and item is legacy, it passes
        if (hasLegacyFilter && item.isLegacy) {
          // Legacy item passes
        }
        // If other rarities are selected and item matches one of them, it passes
        else if (hasOtherRarityFilters && filterState.rarity.includes(item.rarity)) {
          // Regular rarity item passes
        }
        // If neither condition is met, filter out
        else {
          return false
        }
      }
      
      return true
    })

    // Sort items
    if (filterState.sort) {
      filtered.sort((a, b) => {
        switch (filterState.sort) {
          case 'price-high':
            return b.price - a.price
          case 'price-low':
            return a.price - b.price
          case 'name-asc':
            return a.name.localeCompare(b.name)
          case 'name-desc':
            return b.name.localeCompare(a.name)
          default:
            return 0
        }
      })
    }

    return filtered
  }, [filterState])

  const getPaginatedItems = useCallback((items: StoreItem[], itemsPerPage: number) => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return items.slice(startIndex, endIndex)
  }, [currentPage])

  const getTotalPages = useCallback((items: StoreItem[], itemsPerPage: number) => {
    return Math.ceil(items.length / itemsPerPage)
  }, [])

  return {
    // State
    filterState,
    currentPage,
    
    // Actions
    setSearch,
    setRarity,
    setType,
    setPriceRange,
    setSort,
    setCurrentPage,
    
    // Filter management
    selectedFilters,
    removeFilter,
    clearCategory,
    clearAllFilters,
    
    // Data processing
    getFilteredItems,
    getPaginatedItems,
    getTotalPages
  }
}
