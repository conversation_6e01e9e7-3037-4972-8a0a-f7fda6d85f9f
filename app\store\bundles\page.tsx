import { StoreClient } from "@/components/store/store-client"

// Mock data for bundles since we don't have a real API
const mockBundles = Array.from({ length: 45 }, (_, i) => ({
  id: 60000 + i,
  name: `Bundle ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `Bundle ${i + 1} description`,
  imageUrl: '/placeholder-bundle.jpg',
  price: Math.floor(Math.random() * 150) + 50,
  type: 'Bundle'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function BundlesPage() {
  return <StoreClient initialItems={mockBundles} initialFilter={{ type: ['Bundle'] }} />
}
