import { StoreClient } from "@/components/store/store-client"

// Types for the Community Dragon API
interface SkinLine {
  id: number
}

interface ChromaRarity {
  region: string
  rarity: number
}

interface ChromaDescription {
  region: string
  description: string
}

interface Chroma {
  id: number
  name: string
  contentId: string
  skinClassification: string
  chromaPath: string
  tilePath: string
  colors: string[]
  descriptions: ChromaDescription[]
  description: string
  rarities: ChromaRarity[]
}

interface Skin {
  id: number
  contentId: string
  isBase: boolean
  name: string
  skinClassification: string
  splashPath: string
  uncenteredSplashPath: string
  tilePath: string
  loadScreenPath: string
  skinType: string
  rarity: string
  isLegacy: boolean
  splashVideoPath: string | null
  previewVideoUrl: string | null
  collectionSplashVideoPath: string | null
  collectionCardHoverVideoPath: string | null
  featuresText: string | null
  chromaPath: string | null
  chromas?: Chroma[]
  emblems: any | null
  regionRarityId: number
  rarityGemPath: string | null
  skinLines: SkinLine[] | null
  description: string | null
}

interface SkinsResponse {
  [key: string]: Skin
}

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

// This function runs at build time and on revalidation
async function getStoreData() {
  try {
    const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json', {
      next: { revalidate: 3600 }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch skins data')
    }

    const skinsData: SkinsResponse = await response.json()

    // Process skins
    const skins = Object.values(skinsData)
      .filter(skin => !skin.isBase) // Exclude base skins
      .map(skin => ({
        id: skin.id,
        name: skin.name,
        rarity: skin.rarity === 'kNoRarity' ? 'Rare' : skin.rarity.replace('k', ''),
        isLegacy: skin.isLegacy,
        description: skin.description || `${skin.name} skin`,
        imageUrl: `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${skin.splashPath.toLowerCase().replace('/lol-game-data/assets', '')}`,
        price: generatePrice(skin.rarity, skin.isLegacy),
        type: 'Skin'
      }))

    // Process chromas
    const chromas: any[] = []

    Object.values(skinsData).forEach(skin => {
      // Skip base skins and skins without chromas
      if (skin.isBase || !skin.chromas || skin.chromas.length === 0) {
        return
      }

      // Process each chroma in the skin
      skin.chromas.forEach(chroma => {
        // Only include chromas with skinClassification "kRecolor"
        if (chroma.skinClassification === 'kRecolor') {
          // Get rarity from the chroma's rarities array (default to 0 if not found)
          const rarityData = chroma.rarities?.find(r => r.region === 'riot')
          const rarityNumber = rarityData?.rarity || 0

          // Convert rarity number to string
          const rarityString = getRarityString(rarityNumber)

          // Extract and process the chroma image path
          let imageUrl = ''
          if (chroma.chromaPath) {
            // Remove /lol-game-data/assets prefix and make lowercase
            const processedPath = chroma.chromaPath.toLowerCase().replace('/lol-game-data/assets', '')
            imageUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${processedPath}`
          }

          chromas.push({
            id: chroma.id,
            name: chroma.name,
            rarity: rarityString,
            isLegacy: false, // Chromas are typically not legacy
            description: chroma.description || `${chroma.name} chroma`,
            imageUrl: imageUrl,
            price: generateChromaPrice(rarityNumber),
            type: 'Chroma'
          })
        }
      })
    })

    // Combine skins and chromas
    const allItems = [...skins, ...chromas]

    return {
      items: allItems,
      lastUpdated: new Date().toISOString(),
    }
  } catch (error) {
    console.error('Error fetching store data:', error)
    return {
      items: [],
      lastUpdated: new Date().toISOString(),
    }
  }
}

// Convert rarity number to string based on the mapping provided
function getRarityString(rarityNumber: number): string {
  switch (rarityNumber) {
    case 1:
      return 'Rare'
    case 2:
      return 'Epic'
    case 3:
      return 'Legendary'
    case 4:
      return 'Mythic'
    case 5:
      return 'Ultimate'
    case 6:
      return 'Exalted'
    case 7:
      return 'Transcendent'
    default:
      return 'Others' // For 0 or any other value
  }
}

// Generate price based on chroma rarity number
function generateChromaPrice(rarityNumber: number): number {
  let basePrice = 5 // Chromas are generally cheaper than skins

  switch (rarityNumber) {
    case 0: // Others
      basePrice = 5
      break
    case 1: // Rare
      basePrice = 8
      break
    case 2: // Epic
      basePrice = 12
      break
    case 3: // Legendary
      basePrice = 18
      break
    case 4: // Mythic
      basePrice = 25
      break
    case 5: // Ultimate
      basePrice = 35
      break
    case 6: // Exalted
      basePrice = 50
      break
    case 7: // Transcendent
      basePrice = 75
      break
    default:
      basePrice = 5
  }

  return basePrice
}

// Generate price based on rarity and legacy status
function generatePrice(rarity: string, isLegacy: boolean): number {
  let basePrice = 10

  switch (rarity) {
    case 'kNoRarity':
      basePrice = 10
      break
    case 'kEpic':
      basePrice = 25
      break
    case 'kLegendary':
      basePrice = 50
      break
    case 'kMythic':
      basePrice = 75
      break
    case 'kUltimate':
      basePrice = 100
      break
    case 'kExalted':
      basePrice = 150
      break
    case 'kTranscendent':
      basePrice = 200
      break
    default:
      basePrice = 10
  }

  // Add premium for legacy skins
  if (isLegacy) {
    basePrice = Math.floor(basePrice * 1.5)
  }

  return basePrice
}

export default async function StorePage() {
  // Fetch data at build time and on revalidation
  const storeData = await getStoreData()

  return <StoreClient initialItems={storeData.items} />
}