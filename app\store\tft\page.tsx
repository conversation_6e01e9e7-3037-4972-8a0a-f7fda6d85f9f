import { StoreClient } from "@/components/store/store-client"

// Mock data for TFT items since we don't have a real API
const mockTftItems = Array.from({ length: 178 }, (_, i) => ({
  id: 70000 + i,
  name: `TFT Item ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `TFT Item ${i + 1} description`,
  imageUrl: '/placeholder-tft.jpg',
  price: Math.floor(Math.random() * 60) + 15,
  type: 'TFT'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function TftPage() {
  return <StoreClient initialItems={mockTftItems} initialFilter={{ type: ['TFT'] }} />
}
