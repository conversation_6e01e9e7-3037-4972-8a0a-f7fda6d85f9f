import { StoreClient } from "@/components/store/store-client"

// Types for the Community Dragon API
interface SkinLine {
  id: number
}

interface Skin {
  id: number
  contentId: string
  isBase: boolean
  name: string
  skinClassification: string
  splashPath: string
  uncenteredSplashPath: string
  tilePath: string
  loadScreenPath: string
  skinType: string
  rarity: string
  isLegacy: boolean
  splashVideoPath: string | null
  previewVideoUrl: string | null
  collectionSplashVideoPath: string | null
  collectionCardHoverVideoPath: string | null
  featuresText: string | null
  chromaPath: string | null
  emblems: any | null
  regionRarityId: number
  rarityGemPath: string | null
  skinLines: SkinLine[] | null
  description: string | null
}

interface SkinsResponse {
  [key: string]: Skin
}

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

// This function runs at build time and on revalidation
async function getStoreData() {
  try {
    const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json', {
      next: { revalidate: 3600 }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch skins data')
    }

    const skinsData: SkinsResponse = await response.json()

    // Filter out base skins and transform the data
    const skins = Object.values(skinsData)
      .filter(skin => !skin.isBase) // Exclude base skins
      .map(skin => ({
        id: skin.id,
        name: skin.name,
        rarity: skin.rarity === 'kNoRarity' ? 'Rare' : skin.rarity.replace('k', ''),
        isLegacy: skin.isLegacy,
        description: skin.description,
        imageUrl: `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${skin.splashPath.toLowerCase().replace('/lol-game-data/assets', '')}`,
        price: generatePrice(skin.rarity, skin.isLegacy), // Generate a price based on rarity
        type: 'Chroma'
      }))

    return {
      items: skins,
      lastUpdated: new Date().toISOString(),
    }
  } catch (error) {
    console.error('Error fetching store data:', error)
    return {
      items: [],
      lastUpdated: new Date().toISOString(),
    }
  }
}

// Generate price based on rarity and legacy status
function generatePrice(rarity: string, isLegacy: boolean): number {
  let basePrice = 10

  switch (rarity) {
    case 'kNoRarity':
      basePrice = 10
      break
    case 'kEpic':
      basePrice = 25
      break
    case 'kLegendary':
      basePrice = 50
      break
    case 'kMythic':
      basePrice = 75
      break
    case 'kUltimate':
      basePrice = 100
      break
    case 'kExalted':
      basePrice = 150
      break
    case 'kTranscendent':
      basePrice = 200
      break
    default:
      basePrice = 10
  }

  // Add premium for legacy skins
  if (isLegacy) {
    basePrice = Math.floor(basePrice * 1.5)
  }

  return basePrice
}

export default async function ChromasPage() {
  // Fetch data at build time and on revalidation
  const storeData = await getStoreData()

  return <StoreClient initialItems={storeData.items} initialFilter={{ type: ['Chroma'] }} />
}
