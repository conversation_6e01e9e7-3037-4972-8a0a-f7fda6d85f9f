import { StoreClient } from "@/components/store/store-client"

// Mock data for chests since we don't have a real API
const mockChests = Array.from({ length: 89 }, (_, i) => ({
  id: 30000 + i,
  name: `Chest ${i + 1}`,
  rarity: ['Rare', 'Epic', 'Legendary'][Math.floor(Math.random() * 3)],
  isLegacy: Math.random() > 0.8,
  description: `Chest ${i + 1} description`,
  imageUrl: '/placeholder-chest.jpg',
  price: Math.floor(Math.random() * 30) + 5,
  type: 'Chest'
}))

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function ChestsPage() {
  return <StoreClient initialItems={mockChests} initialFilter={{ type: ['Chest'] }} />
}
